'use client'

import {
  DescriptionDetail,
  DescriptionGroup,
  DescriptionList,
  DescriptionTerm,
} from '@/components/custom-ui/description-list'
import { Badge } from '@/components/ui/badge'
import { FaCircleCheck } from 'react-icons/fa6'
import { FiLoader } from 'react-icons/fi'

export function CompanySummary() {
  // Use a fixed status for now - this should be replaced with actual data from API
  const isActive = true

  return (
    <DescriptionList>
      <DescriptionGroup>
        <DescriptionTerm className="">Trạng thái</DescriptionTerm>
        <DescriptionDetail>
          <Badge
            variant="outline"
            className="text-muted-foreground px-1.5">
            {isActive ? (
              <>
                <FaCircleCheck className="fill-green-500 dark:fill-green-400" /> Đang hoạt động
              </>
            ) : (
              <>
                <FiLoader /> Không hoạt động
              </>
            )}
          </Badge>
        </DescriptionDetail>
      </DescriptionGroup>
    </DescriptionList>
  )
}
